from imports import *

import asyncio
import ollama
import time

from llama_index.core.query_engine import BaseQueryEngine
from llama_index.core.vector_stores import MetadataFilters
from managers.manager_supervisors import SupervisorManager
from langchain_core.messages import  HumanMessage, SystemMessage
#from managers.manager_agno import AgnoManager

# Simple circuit breaker to track failures
class SimpleCircuitBreaker:
    def __init__(self, failure_threshold=3, reset_timeout=300):  # 5 minutes
        self.failure_count = 0
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.last_failure_time = 0
        self.is_open = False

    def record_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.is_open = True
            print(f"[DEBUG] Circuit breaker opened after {self.failure_count} failures")

    def record_success(self):
        self.failure_count = 0
        self.is_open = False
        print(f"[DEBUG] Circuit breaker reset after successful operation")

    def should_use_simple_mode(self):
        if not self.is_open:
            return False

        # Check if enough time has passed to try again
        if time.time() - self.last_failure_time > self.reset_timeout:
            self.is_open = False
            self.failure_count = 0
            print(f"[DEBUG] Circuit breaker reset after timeout")
            return False

        return True

# Global circuit breaker instance
retrieval_circuit_breaker = SimpleCircuitBreaker()




# # Create the llama_index OpenAI LLM instance
# llm = OpenAI(
#     model="gpt-4-mini",
#     temperature=0,
#     max_tokens=2000
# )
# reranker = LLMRerank(top_n=5, llm=llm)

async def retrieve_data(query_engine: BaseQueryEngine, user_input: str) -> str:
    """Perform RAG search using the provided query engine with timeout protection and circuit breaker"""
    try:
        print(f"[DEBUG] Starting retrieve_data for: {user_input}")

        # Check circuit breaker - if it's open, use simple mode
        if retrieval_circuit_breaker.should_use_simple_mode():
            print(f"[DEBUG] Circuit breaker is open, using simple retrieval mode")
            result = await simple_retrieve_data(query_engine, user_input)
            if result and "failed" not in result.lower() and "timed out" not in result.lower():
                retrieval_circuit_breaker.record_success()
            return result

        print(f"[DEBUG] About to call enhanced RAG search tool...")

        # Use the enhanced retrieval system with timeout protection
        from tools.inputs.task_retrieval import rag_search_tool

        print(f"[DEBUG] Using enhanced RAG search tool with 30 second timeout...")

        # Add timeout to prevent hanging
        try:
            result = await asyncio.wait_for(
                rag_search_tool.ainvoke({"query": user_input}),
                timeout=30.0  # 30 second timeout
            )

            if result and "No relevant information found" not in result:
                print(f"[DEBUG] Enhanced retrieval successful")
                retrieval_circuit_breaker.record_success()
                return result
            else:
                print(f"[DEBUG] Enhanced retrieval returned no results, falling back to basic query")
                # Fallback to basic response if no results
                response = await asyncio.wait_for(
                    query_engine.aquery(user_input),
                    timeout=15.0  # 15 second timeout for basic query
                )
                print(f"[DEBUG] Basic query completed, response: {response}")
                retrieval_circuit_breaker.record_success()
                return f"{response}"

        except asyncio.TimeoutError:
            print(f"[DEBUG] Enhanced retrieval timed out, falling back to basic query")
            retrieval_circuit_breaker.record_failure()
            # Fallback to basic response if timeout
            response = await asyncio.wait_for(
                query_engine.aquery(user_input),
                timeout=15.0  # 15 second timeout for basic query
            )
            print(f"[DEBUG] Basic query completed after timeout, response: {response}")
            return f"{response}"

    except asyncio.TimeoutError:
        print(f"[DEBUG] All retrieval methods timed out")
        retrieval_circuit_breaker.record_failure()
        return f"Retrieval timed out. Please try a simpler query or try again later."
    except Exception as e:
        print(f"[DEBUG] Error in retrieve_data: {e}")
        retrieval_circuit_breaker.record_failure()
        etc.helper_functions.exception_triggered(e)

        # Try a simple fallback without the enhanced system
        try:
            print(f"[DEBUG] Attempting simple retrieval fallback...")
            result = await simple_retrieve_data(query_engine, user_input)
            if result and "failed" not in result.lower() and "timed out" not in result.lower():
                retrieval_circuit_breaker.record_success()
            return result
        except Exception as fallback_error:
            print(f"[DEBUG] Simple fallback also failed: {fallback_error}")
            return f"All retrieval methods failed. Original error: {e}, Fallback error: {fallback_error}"



async def query_data(user_input: str) -> str:
    """Perform generic search using the provided query engine"""
    try:
        result = await ZairaSettings.llm.ainvoke(
            input=[
                SystemMessage("Your sole purpose is to retrieve a short burst of data in order to be used as input for a large language model."
                              "Do not actually answer the request as you will not be able to retrieve all relevant information yourself."
                              "Once your response has been provided, it will be combined with company-specific data and then put into a new LLM call."),
                HumanMessage(f"User input: {user_input}"),
            ]
        )
        if isinstance(result, etc.helper_functions.get_any_message_as_type()):
            result = result.content
        return result#Command(update={"messages":result})
    except Exception as e:
        etc.helper_functions.exception_triggered(e)






async def simple_retrieve_data(query_engine: BaseQueryEngine, user_input: str) -> str:
    """Simple retrieval function that bypasses complex operations"""
    try:
        print(f"[DEBUG] Using simple retrieval for: {user_input}")

        # Use the basic query engine directly with timeout
        response = await asyncio.wait_for(
            query_engine.aquery(user_input),
            timeout=10.0
        )

        print(f"[DEBUG] Simple retrieval completed")
        return f"{response}"

    except asyncio.TimeoutError:
        print(f"[DEBUG] Simple retrieval timed out")
        return "Simple retrieval timed out. Please try a shorter query."
    except Exception as e:
        print(f"[DEBUG] Simple retrieval failed: {e}")
        return f"Simple retrieval failed: {e}"


async def complexity_data(user_input: str) -> int:
    return 1



async def parse_user_input_query(query_engine:BaseQueryEngine, user_input:str) -> str:
    retrieved_data, queried_data, complexity_scalar = await asyncio.gather(
            retrieve_data(query_engine, user_input),
            query_data(user_input),
            complexity_data(user_input)
        )
    if not retrieved_data and not queried_data:
        print("No relevant data found for your query.")
        return ""
    return etc.helper_functions.convert_key_value_to_string(retrieved_data=retrieved_data, query_data=queried_data, complexity=complexity_scalar)