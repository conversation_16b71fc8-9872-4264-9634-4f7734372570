from imports import *

import asyncio
import ollama

from llama_index.core.query_engine import BaseQueryEngine
from llama_index.core.vector_stores import MetadataFilters
from managers.manager_supervisors import SupervisorManager
from langchain_core.messages import  HumanMessage, SystemMessage
#from managers.manager_agno import AgnoManager




# # Create the llama_index OpenAI LLM instance
# llm = OpenAI(
#     model="gpt-4-mini",
#     temperature=0,
#     max_tokens=2000
# )
# reranker = LLMRerank(top_n=5, llm=llm)

async def retrieve_data(query_engine: BaseQueryEngine, user_input: str) -> str:
    """Perform RAG search using the provided query engine"""
    try:
        print(f"[DEBUG] Starting retrieve_data for: {user_input}")
        print(f"[DEBUG] About to call query_engine.aquery...")

        # Use the enhanced retrieval system instead of the basic one
        from tools.inputs.task_retrieval import rag_search_tool

        print(f"[DEBUG] Using enhanced RAG search tool...")
        result = await rag_search_tool.ainvoke({"query": user_input})

        if result and "No relevant information found" not in result:
            print(f"[DEBUG] Enhanced retrieval successful")
            return result
        else:
            print(f"[DEBUG] Enhanced retrieval returned no results, falling back to basic query")
            # Fallback to basic response if no results
            response = await query_engine.aquery(user_input)
            print(f"[DEBUG] Basic query completed, response: {response}")
            return f"{response}"

    except Exception as e:
        print(f"[DEBUG] Error in retrieve_data: {e}")
        etc.helper_functions.exception_triggered(e)

        # Try a simple fallback without the enhanced system
        try:
            print(f"[DEBUG] Attempting basic fallback query...")
            response = await query_engine.aquery(user_input)
            print(f"[DEBUG] Fallback query completed")
            return f"{response}"
        except Exception as fallback_error:
            print(f"[DEBUG] Fallback also failed: {fallback_error}")
            return f"Unable to retrieve information: {e}"



async def query_data(user_input: str) -> str:
    """Perform generic search using the provided query engine"""
    try:
        result = await ZairaSettings.llm.ainvoke(
            input=[
                SystemMessage("Your sole purpose is to retrieve a short burst of data in order to be used as input for a large language model."
                              "Do not actually answer the request as you will not be able to retrieve all relevant information yourself."
                              "Once your response has been provided, it will be combined with company-specific data and then put into a new LLM call."),
                HumanMessage(f"User input: {user_input}"),
            ]
        )
        if isinstance(result, etc.helper_functions.get_any_message_as_type()):
            result = result.content
        return result#Command(update={"messages":result})
    except Exception as e:
        etc.helper_functions.exception_triggered(e)






async def complexity_data(user_input: str) -> int:
    return 1



async def parse_user_input_query(query_engine:BaseQueryEngine, user_input:str) -> str:
    retrieved_data, queried_data, complexity_scalar = await asyncio.gather(
            retrieve_data(query_engine, user_input),
            query_data(user_input),
            complexity_data(user_input)
        )
    if not retrieved_data and not queried_data:
        print("No relevant data found for your query.")
        return ""
    return etc.helper_functions.convert_key_value_to_string(retrieved_data=retrieved_data, query_data=queried_data, complexity=complexity_scalar)