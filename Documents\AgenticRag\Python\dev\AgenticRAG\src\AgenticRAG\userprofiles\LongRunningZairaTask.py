from imports import *

from asyncio import sleep, Task, create_task
import time
from uuid import UUID
from typing import Dict, Any, Optional, Awaitable
from random import choice, randint
from time import time as time_time
from threading import Lock
from pydantic import BaseModel, Field, ConfigDict

from managers.manager_supervisors import SupervisorManager
from inputs.user_query import parse_user_input_query
from endpoints.mybot_generic import MyBot_Generic

class LongRunningZairaTask(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser

    """Handles a single query requested by a user"""
    human_in_the_loop_callback: Optional[Awaitable] = None
    task_status: Dict[str, Dict[str, Any]] = {}
    _lock: Lock = None
    task_id: UUID = None
    complete_message: str = ""
    user: "ZairaUser" = None
    #asyncio_Task: asyncio.Task = None
    calling_bot: MyBot_Generic = None
    original_physical_message: Optional[Any] = None # Type-agnostic. Gets filled based on the source. DO NOT CALL OR CAST DIRECTLY!
    asyncio_Task_await_response: Task = Field(None, exclude=True)
    asyncio_Task_long_running_query: Task = Field(None, exclude=True)
    chat_history_on_start: list[str] = []
    
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, user: "ZairaUser", task_id: UUID, complete_message: str, calling_bot: MyBot_Generic, chat_history_on_start: list[str], original_message):
        super().__init__()
        self.task_status: Dict[str, Dict[str, Any]] = {}
        self._lock = Lock()
        self.task_id = task_id
        self.complete_message = complete_message
        self.user = user
        self.calling_bot = calling_bot
        self.chat_history_on_start = chat_history_on_start
        self.original_physical_message = original_message
        self._set_task_status({
                'status': 'running',
                'message': f'Task {str(task_id)} started!',
                'started_at': time_time()
            })
        LogFire.log("INIT", f"New task started with question of length: {len(self.complete_message)}.", f"Question: {self.complete_message}")

    async def run_task(self):
        lowered = self.complete_message.lower()
        parts = lowered.split(' ', 1)  # Split on first space
        first_word: str = parts[0]
        user_message = self.complete_message
        if len(first_word) > 0:
            if first_word[0] == "!" or first_word[0] == "?":
                if len(parts) > 1:
                    # Redefine user_message as everything after the command
                    user_message = self.complete_message[1:]

        async def long_running_query(command:str, user_message: str):
            try:
                original_status = self.get_task_status()
                # if command == '':
                #     response = f'Well, you\'re awfully silent...'
                # elif 'hello' == command:
                #     response = f'Hello there!'
                # elif 'howareyou' == command:
                #     response = f'Good, thanks!'
                # elif 'rolldice' in command:
                #     response = f'You rolled: {randint(1, 6)}'
                # else 'zaira' in command:
                try:
                    await self.calling_bot.send_reply(f"Starting a process with a maximum time of {TIMEOUT_LIMIT / 60} minutes.", self, self.original_physical_message)
                    # Format retrieved data for the agents
                    #retrieval_summary = ""
                    print(f"[DEBUG] LongRunningZairaTask: About to call parse_user_input_query for: {user_message}")
                    retrieval_summary = await parse_user_input_query(Globals.get_query_engine_default(), user_message)
                    print(f"[DEBUG] LongRunningZairaTask: parse_user_input_query completed, summary length: {len(retrieval_summary) if retrieval_summary else 0}")
                    
                    # Create enhanced prompt with our retrieved context
                    enhanced_prompt = etc.helper_functions.convert_key_value_to_string(User_Question=user_message,chat_history=self.chat_history_on_start, Start_of_relevant_information_from_our_knowledge_base=retrieval_summary) + "\n\nEnd of relevant information."
                    
                    # Run the agent architecture with our enhanced prompt
                    top_level_supervisor = SupervisorManager.get_supervisor("top_level_supervisor")
                    result = await top_level_supervisor.call_supervisor(user_message, self.user) # enhanced_prompt
                    response = result["result"]
                    call_trace = result["call_trace"]

                    # Print the result
                    async def wait_for_no_task_python_log(self: "LongRunningZairaTask", call_trace):
                        while True:
                            # Wait with outputting to the Python log until there's nothing being logged anymore
                            await sleep(1)
                            if self.user.my_task == None:
                                break
                        print("Call trace:")
                        for call in call_trace:
                            print(call)
                    self.asyncio_Task_long_running_query = create_task(wait_for_no_task_python_log(self, result["call_trace"]))
                    self.asyncio_Task_long_running_query.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
                except Exception as e:
                    etc.helper_functions.exception_triggered(e)
                    response = f"{e}"
                if self.get_task_status() == original_status:
                    self._set_task_status({
                        'status': "completed",
                        'message': response,
                        'call_trace': call_trace,
                        'completed_at': time.time()
                    })
            except Exception as e:
                self._set_task_status({
                    'status': "failed",
                    'message': f'Task {self.task_id} failed: {str(e)}',
                    'completed_at': time.time()
                })
            return

        # Define handler for zaira query
        await long_running_query(command=first_word, user_message=user_message) # Separate function to ensure we handle end-of-life calls

    def get_task_status(self) -> Dict[str, Any]:
        with self._lock:
            return self.task_status
        
    def _set_task_status(self, new_status:Dict[str, Any]):
        with self._lock:
            self.task_status.update(new_status)

    async def on_message(self, complete_message: str):
        self.on_message(complete_message=complete_message, calling_bot=self.calling_bot, attachments=[], original_message=self.original_physical_message) 
    async def on_message(self, complete_message: str, calling_bot: MyBot_Generic, original_message = None): # Second version exists to keep consistency with the ZairaUser function equivalent. That one needs the parameters for start_task
        if self.human_in_the_loop_callback == None:
            # Task is still running, do not allow a new one
            await self.calling_bot.send_reply("Zaira is currently working, please hold", self, self.original_physical_message)
        else:
            # We're expecting human-in-the-loop, feed it into the callback
            hitl_callback = self.human_in_the_loop_callback
            self.human_in_the_loop_callback = None
            response = await hitl_callback(self, complete_message)

    async def request_human_in_the_loop(self, request: str, callback: Awaitable, halt_until_response = False):
        if self.human_in_the_loop_callback != None:
            raise RuntimeError(f"Request '{request}' for human-in-the-loop while we're still waiting on user input")
        self.human_in_the_loop_callback = callback
        await self.calling_bot.request_human_in_the_loop_internal(request=request, task=self, message=self.original_physical_message, halt_until_response=halt_until_response)
        
    async def await_status_complete(self, wait_on_complete = False):
        async def long_running_query(self: "LongRunningZairaTask"):
            while True:
                my_status = self.get_task_status()
                status = my_status["status"]
                await sleep(1)
                if status != "running":
                    await _handle_response(self, my_status["message"], "\n".join(my_status["call_trace"] if "call_trace" in my_status else ""))
                    break

        async def _handle_response(self: "LongRunningZairaTask", message: str, call_trace: str):
            output_supervisor = SupervisorManager.get_supervisor("top_output_supervisor")
            # Add call trace
            await output_supervisor.call_supervisor(query=f"___Original source___: {self.calling_bot.name}.\n___Original input___: {self.complete_message}.\n___Call trace___: {call_trace}.\n___Output message___: {message}", user=self.user)
            # In case the output supervisor is requesting HITML, don't end the task yet!
            if self.human_in_the_loop_callback == None:
                # Remove the reference to this task so that a new one can be started
                self.user.my_task = None
            else:
                self._set_task_status({
                    'status': "running",
                })
                self.asyncio_Task_await_response = create_task(long_running_query(self))
                self.asyncio_Task_await_response.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
            
        my_status = self.get_task_status()
        if my_status["status"] == "running":
            if wait_on_complete == True:
                await long_running_query(self)
            else:
                if Globals.get_debug() == True and self.calling_bot.parent_instance == None:
                    await long_running_query(self)
                else:
                    # Create new thread that waits for the message to be completed and then sends it
                    self.asyncio_Task_await_response = create_task(long_running_query(self))
                    self.asyncio_Task_await_response.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
        elif my_status["status"] == "completed":
            await _handle_response(self, my_status["message"], my_status["call_trace"])
        else:
            await self.send_response("Last request has failed.")

    async def send_response(self, answer: str):
        await self.calling_bot.send_reply(answer, self, self.original_physical_message)

from userprofiles.ZairaUser import ZairaUser
LongRunningZairaTask.model_rebuild()
