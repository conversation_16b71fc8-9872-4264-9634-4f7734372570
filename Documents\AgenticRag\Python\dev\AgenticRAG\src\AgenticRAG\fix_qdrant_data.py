#!/usr/bin/env python3
"""
<PERSON>ript to fix Qdrant data by removing or fixing nodes with None text content
"""
import asyncio
from qdrant_client import QdrantClient, AsyncQdrantClient
import json

async def fix_qdrant_data():
    """Fix nodes with None text content in Qdrant collection"""
    try:
        # Connect to Qdrant
        aclient = AsyncQdrantClient(url="http://localhost:6333", timeout=30)
        
        print("=== Fixing Qdrant Data ===")
        
        # Get all points from the collection
        print("Fetching all points from mainCollection...")
        
        points_to_delete = []
        points_to_update = []
        total_points = 0
        
        # Scroll through all points
        offset = None
        while True:
            scroll_result = await aclient.scroll(
                collection_name="mainCollection",
                limit=100,
                with_payload=True,
                with_vectors=False,
                offset=offset
            )
            
            points, next_offset = scroll_result
            
            if not points:
                break
                
            for point in points:
                total_points += 1
                
                if point.payload:
                    # Check if _node_content exists and is valid
                    node_content = point.payload.get('_node_content')
                    
                    if node_content is None:
                        print(f"Found point with None content: {point.id}")
                        points_to_delete.append(point.id)
                    elif isinstance(node_content, str):
                        # Try to parse as JSON to check if it's a serialized node
                        try:
                            parsed_content = json.loads(node_content)
                            if isinstance(parsed_content, dict):
                                # Check if this is a serialized TextNode with null text
                                if parsed_content.get('text') is None:
                                    print(f"Found point with None text in JSON: {point.id}")
                                    print(f"  Content preview: {node_content[:200]}...")
                                    points_to_delete.append(point.id)
                                elif parsed_content.get('embedding') is None and 'id_' in parsed_content:
                                    # This looks like a serialized TextNode, check if it has valid text
                                    print(f"Found serialized TextNode: {point.id}")
                                    print(f"  Text value: {repr(parsed_content.get('text'))}")
                                    if parsed_content.get('text') is None:
                                        points_to_delete.append(point.id)
                        except json.JSONDecodeError:
                            # It's just a regular string, which is fine
                            pass
                    elif node_content == "":
                        print(f"Found point with empty content: {point.id}")
                        points_to_delete.append(point.id)
                else:
                    print(f"Found point with no payload: {point.id}")
                    points_to_delete.append(point.id)
            
            offset = next_offset
            if offset is None:
                break
        
        print(f"\nTotal points processed: {total_points}")
        print(f"Points to delete: {len(points_to_delete)}")
        
        if points_to_delete:
            print(f"\nDeleting {len(points_to_delete)} problematic points...")
            
            # Delete points in batches
            batch_size = 100
            for i in range(0, len(points_to_delete), batch_size):
                batch = points_to_delete[i:i + batch_size]
                await aclient.delete(
                    collection_name="mainCollection",
                    points_selector=batch
                )
                print(f"Deleted batch {i//batch_size + 1}/{(len(points_to_delete) + batch_size - 1)//batch_size}")
            
            print(f"✅ Successfully deleted {len(points_to_delete)} problematic points")
        else:
            print("✅ No problematic points found")
        
        # Get final collection info
        collection_info = await aclient.get_collection("mainCollection")
        print(f"\nFinal collection status:")
        print(f"Points count: {collection_info.points_count}")
        print(f"Status: {collection_info.status}")
        
    except Exception as e:
        print(f"❌ Error fixing Qdrant data: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            await aclient.close()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(fix_qdrant_data())
