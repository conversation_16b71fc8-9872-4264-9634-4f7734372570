#!/usr/bin/env python3
"""
Simple script to check Qdrant collection status and data
"""
import asyncio
from qdrant_client import QdrantClient, AsyncQdrantClient

async def check_qdrant_collection():
    """Check if Qdrant collection exists and has data"""
    try:
        # Connect to Qdrant
        client = QdrantClient(url="http://localhost:6333", timeout=30)
        aclient = AsyncQdrantClient(url="http://localhost:6333", timeout=30)
        
        print("=== Qdrant Collection Status ===")
        
        # Check if collection exists
        try:
            collection_info = await aclient.get_collection("mainCollection")
            print(f"✅ Collection 'mainCollection' exists")
            print(f"   Points count: {collection_info.points_count}")
            print(f"   Vectors count: {collection_info.vectors_count}")
            print(f"   Status: {collection_info.status}")
            
            if collection_info.points_count == 0:
                print("⚠️  Collection is EMPTY - no data to query!")
                return False
            else:
                print(f"✅ Collection has {collection_info.points_count} points")
                
                # Try to get a few sample points
                try:
                    scroll_result = await aclient.scroll(
                        collection_name="mainCollection",
                        limit=3,
                        with_payload=True,
                        with_vectors=False
                    )
                    
                    print(f"\n=== Sample Data (first 3 points) ===")
                    for i, point in enumerate(scroll_result[0]):
                        print(f"Point {i+1}:")
                        print(f"  ID: {point.id}")
                        if point.payload:
                            # Show some payload info
                            content = point.payload.get('_node_content', 'No content')
                            content_preview = content[:100] + "..." if len(content) > 100 else content
                            print(f"  Content preview: {content_preview}")
                            
                            # Show other metadata
                            for key, value in point.payload.items():
                                if key != '_node_content':
                                    print(f"  {key}: {value}")
                        print()
                        
                except Exception as e:
                    print(f"❌ Error getting sample data: {e}")
                
                return True
                
        except Exception as e:
            print(f"❌ Collection 'mainCollection' does not exist or error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to connect to Qdrant: {e}")
        return False
    finally:
        try:
            await aclient.close()
        except:
            pass

if __name__ == "__main__":
    result = asyncio.run(check_qdrant_collection())
    if not result:
        print("\n💡 Suggestion: You may need to crawl some data first or check if Qdrant is running properly.")
